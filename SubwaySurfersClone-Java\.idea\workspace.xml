<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="707af57b-8fd0-4306-8c92-fbb29671732a" name="Changes" comment="Rotar jugador mientras salta" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 6
}</component>
  <component name="ProjectId" id="2nj3ZZVkOw4LUKWDPECVGxcuLDT" />
  <component name="ProjectLevelVcsManager" settingsEditedManually="true" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Application.Main.executor": "Run",
    "RunOnceActivity.OpenProjectViewOnStart": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "git-widget-placeholder": "master",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "C:/Users/<USER>/IdeaProjects/SubwaySurfersClone/src/assets/obstacle",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\IdeaProjects\SubwaySurfersClone\src\assets\obstacle" />
      <recent name="C:\Users\<USER>\IdeaProjects\SubwaySurfersClone\src\assets" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\IdeaProjects\SubwaySurfersClone\src\assets\player" />
    </key>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="jdk-21.0.2-openjdk-21.0.2-4caba194b151-4f524021" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="707af57b-8fd0-4306-8c92-fbb29671732a" name="Changes" comment="" />
      <created>1729472717607</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1729472717607</updated>
      <workItem from="1729472719047" duration="6327000" />
      <workItem from="1729516605000" duration="7532000" />
      <workItem from="1729688158290" duration="224000" />
      <workItem from="1729898975004" duration="6630000" />
      <workItem from="1729961563402" duration="2005000" />
    </task>
    <task id="LOCAL-00001" summary="Initial project structure">
      <option name="closed" value="true" />
      <created>1729473913196</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1729473913196</updated>
    </task>
    <task id="LOCAL-00002" summary="Update obstacles speed">
      <option name="closed" value="true" />
      <created>1729477567641</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1729477567641</updated>
    </task>
    <task id="LOCAL-00003" summary="Use setlane instead of move method">
      <option name="closed" value="true" />
      <created>1729477597624</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1729477597624</updated>
    </task>
    <task id="LOCAL-00004" summary="Add score label and player movement to scenario">
      <option name="closed" value="true" />
      <created>1729477625307</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1729477625307</updated>
    </task>
    <task id="LOCAL-00005" summary="Add collisions to game">
      <option name="closed" value="true" />
      <created>1729479031237</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1729479031237</updated>
    </task>
    <task id="LOCAL-00006" summary="Add player sprites">
      <option name="closed" value="true" />
      <created>1729479867845</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1729479867845</updated>
    </task>
    <task id="LOCAL-00007" summary="Fix obstacles issue and add jump logic">
      <option name="closed" value="true" />
      <created>1729521930627</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1729521930627</updated>
    </task>
    <task id="LOCAL-00008" summary="Agregar rieles">
      <option name="closed" value="true" />
      <created>1729902658873</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1729902658873</updated>
    </task>
    <task id="LOCAL-00009" summary="Mostrar coins">
      <option name="closed" value="true" />
      <created>1729904024033</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1729904024033</updated>
    </task>
    <task id="LOCAL-00010" summary="Reparar salto">
      <option name="closed" value="true" />
      <created>1729961681440</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1729961681440</updated>
    </task>
    <task id="LOCAL-00011" summary="Diseñar generacion aleatoriai de obstaculos y monedas">
      <option name="closed" value="true" />
      <created>1729962435946</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1729962435946</updated>
    </task>
    <task id="LOCAL-00012" summary="Añadir imagen a los obstaculos">
      <option name="closed" value="true" />
      <created>1729963071553</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1729963071553</updated>
    </task>
    <task id="LOCAL-00013" summary="Desaparecer coins cuando son tocados">
      <option name="closed" value="true" />
      <created>1729963096738</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1729963096738</updated>
    </task>
    <task id="LOCAL-00014" summary="Rotar jugador mientras salta">
      <option name="closed" value="true" />
      <created>1729963499049</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1729963499049</updated>
    </task>
    <option name="localTasksCounter" value="15" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="Initial project structure" />
    <MESSAGE value="Update obstacles speed" />
    <MESSAGE value="Use setlane instead of move method" />
    <MESSAGE value="Add score label and player movement to scenario" />
    <MESSAGE value="Add collisions to game" />
    <MESSAGE value="Add player sprites" />
    <MESSAGE value="Fix obstacles issue and add jump logic" />
    <MESSAGE value="Agregar rieles" />
    <MESSAGE value="Mostrar coins" />
    <MESSAGE value="Reparar salto" />
    <MESSAGE value="Diseñar generacion aleatoriai de obstaculos y monedas" />
    <MESSAGE value="Añadir imagen a los obstaculos" />
    <MESSAGE value="Desaparecer coins cuando son tocados" />
    <MESSAGE value="Rotar jugador mientras salta" />
    <option name="LAST_COMMIT_MESSAGE" value="Rotar jugador mientras salta" />
  </component>
</project>